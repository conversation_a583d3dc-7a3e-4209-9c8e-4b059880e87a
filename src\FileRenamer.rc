#include "resource.h"
#include "main.h"
#include <windows.h>

// 主菜单
IDR_MAIN_MENU MENU
BEGIN
    POPUP "文件(&F)"
    BEGIN
        MENUITEM "退出(&X)",                    ID_MENU_FILE_EXIT
    END
    POPUP "编辑(&E)"
    BEGIN
        MENUITEM "全选(&A)\tCtrl+A",            ID_MENU_EDIT_SELECT_ALL
        MENUITEM "清空列表(&C)\tCtrl+L",        ID_MENU_EDIT_CLEAR_ALL
    END
    POPUP "帮助(&H)"
    BEGIN
        MENUITEM "关于(&A)...",                 ID_MENU_HELP_ABOUT
    END
END

// 右键菜单
IDR_CONTEXT_MENU MENU
BEGIN
    POPUP ""
    BEGIN
        MENUITEM "移除选中项",                  ID_MENU_EDIT_CLEAR_ALL
        MENUITEM SEPARATOR
        MENUITEM "全选",                        ID_MENU_EDIT_SELECT_ALL
    END
END

// 关于对话框
IDD_ABOUT_DIALOG DIALOGEX 0, 0, 250, 150
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "关于 文件批量重命名工具"
FONT 9, "Microsoft YaHei UI"
BEGIN
    ICON            IDI_MAIN_ICON, -1, 20, 20, 20, 20
    LTEXT           "文件批量重命名工具", -1, 60, 20, 120, 12
    LTEXT           "版本 1.0.0", -1, 60, 35, 120, 12
    LTEXT           "一个简单易用的文件批量重命名工具", -1, 60, 50, 150, 12
    LTEXT           "支持正则表达式和多种重命名规则", -1, 60, 65, 150, 12
    LTEXT           "Copyright © 2024", -1, 60, 90, 120, 12
    DEFPUSHBUTTON   "确定", IDOK, 100, 115, 50, 14
END

// 加速键
IDR_ACCELERATOR ACCELERATORS
BEGIN
    "A",            ID_MENU_EDIT_SELECT_ALL,    VIRTKEY, CONTROL
    "L",            ID_MENU_EDIT_CLEAR_ALL,     VIRTKEY, CONTROL
    VK_F5,          ID_BUTTON_PREVIEW,          VIRTKEY
    VK_F9,          ID_BUTTON_EXECUTE,          VIRTKEY
    VK_ESCAPE,      ID_MENU_FILE_EXIT,          VIRTKEY
END

// 字符串表
STRINGTABLE
BEGIN
    IDS_APP_TITLE           "文件批量重命名工具"
    IDS_WINDOW_CLASS        "FileRenamerMainWindow"
    IDS_SELECT_FILES        "选择文件"
    IDS_SELECT_FOLDER       "选择文件夹"
    IDS_CLEAR_LIST          "清空列表"
    IDS_PREVIEW             "预览 (F5)"
    IDS_EXECUTE             "执行 (F9)"
    IDS_UNDO                "撤销"
    IDS_FIND_TEXT           "查找文本:"
    IDS_REPLACE_TEXT        "替换为:"
    IDS_USE_REGEX           "使用正则表达式"
    IDS_ADD_NUMBER          "添加序号"
    IDS_NUMBER_START        "起始值:"
    IDS_NUMBER_STEP         "步长:"
    IDS_ORIGINAL_NAME       "原始名称"
    IDS_NEW_NAME            "新名称"
    IDS_PATH                "路径"
    IDS_SIZE                "大小"
    IDS_DATE_MODIFIED       "修改日期"
    IDS_STATUS_READY        "就绪"
    IDS_STATUS_PROCESSING   "处理中..."
    IDS_STATUS_COMPLETE     "完成"
    IDS_STATUS_ERROR        "错误"
    IDS_CONFIRM_EXECUTE     "确定要重命名选中的文件吗？"
    IDS_CONFIRM_CLEAR       "确定要清空文件列表吗？"
    IDS_ERROR_NO_FILES      "请先选择要重命名的文件"
    IDS_ERROR_INVALID_RULE  "重命名规则无效"
    IDS_ERROR_RENAME_FAILED "重命名失败"
    IDS_SUCCESS_RENAMED     "成功重命名 %d 个文件"
    IDS_ABOUT_TEXT          "文件批量重命名工具\n版本 1.0.0\n\n一个简单易用的文件批量重命名工具\n支持正则表达式和多种重命名规则"
END

// 版本信息
VS_VERSION_INFO VERSIONINFO
FILEVERSION     1,0,0,0
PRODUCTVERSION  1,0,0,0
FILEFLAGSMASK   VS_FFI_FILEFLAGSMASK
FILEFLAGS       0
FILEOS          VOS_NT_WINDOWS32
FILETYPE        VFT_APP
FILESUBTYPE     VFT2_UNKNOWN
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "080404b0"
        BEGIN
            VALUE "CompanyName",        "File Renamer"
            VALUE "FileDescription",    "文件批量重命名工具"
            VALUE "FileVersion",        "*******"
            VALUE "InternalName",       "FileRenamer"
            VALUE "LegalCopyright",     "Copyright © 2024"
            VALUE "OriginalFilename",   "FileRenamer.exe"
            VALUE "ProductName",        "文件批量重命名工具"
            VALUE "ProductVersion",     "*******"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x804, 1200
    END
END
