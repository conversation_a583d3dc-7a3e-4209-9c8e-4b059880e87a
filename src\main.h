#pragma once

#include <windows.h>
#include <commctrl.h>
#include <commdlg.h>
#include <shlobj.h>
#include <shellapi.h>
#include <string>
#include <vector>
#include <memory>

// 应用程序常量
#define APP_NAME L"文件批量重命名工具"
#define APP_VERSION L"1.0.0"
#define WINDOW_CLASS_NAME L"FileRenamerMainWindow"

// 控件ID定义
#define ID_BUTTON_SELECT_FILES      1001
#define ID_BUTTON_SELECT_FOLDER     1002
#define ID_BUTTON_CLEAR_LIST        1003
#define ID_BUTTON_PREVIEW           1004
#define ID_BUTTON_EXECUTE           1005
#define ID_BUTTON_UNDO              1006
#define ID_LISTVIEW_FILES           1007
#define ID_EDIT_FIND_TEXT           1008
#define ID_EDIT_REPLACE_TEXT        1009
#define ID_CHECKBOX_USE_REGEX       1010
#define ID_CHECKBOX_ADD_NUMBER      1011
#define ID_EDIT_NUMBER_START        1012
#define ID_EDIT_NUMBER_STEP         1013
#define ID_LISTVIEW_PREVIEW         1014
#define ID_STATUSBAR                1015
#define ID_PROGRESSBAR              1016
#define ID_COMBO_REGEX_TEMPLATES    1017
#define ID_BUTTON_REGEX_HELP        1018

// 菜单ID定义
#define ID_MENU_FILE_EXIT           2001
#define ID_MENU_EDIT_SELECT_ALL     2002
#define ID_MENU_EDIT_CLEAR_ALL      2003
#define ID_MENU_HELP_ABOUT          2004

// 消息定义
#define WM_UPDATE_PREVIEW           (WM_USER + 1)
#define WM_UPDATE_STATUS            (WM_USER + 2)

// 前向声明
class FileRenamerApp;
class FileManager;
class RenameEngine;
class UIManager;

// 全局变量声明
extern HINSTANCE g_hInstance;
extern FileRenamerApp* g_pApp;
