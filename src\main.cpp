#include "main.h"
#include "FileRenamerApp.h"

// 全局变量定义
HINSTANCE g_hInstance = nullptr;
FileRenamerApp* g_pApp = nullptr;

// 程序入口点
int WINAPI wWinMain(
    _In_ HINSTANCE hInstance,
    _In_opt_ HINSTANCE hPrevInstance,
    _In_ LPWSTR lpCmdLine,
    _In_ int nCmdShow)
{
    UNREFERENCED_PARAMETER(hPrevInstance);
    UNREFERENCED_PARAMETER(lpCmdLine);
    UNREFERENCED_PARAMETER(nCmdShow);

    // 初始化COM
    HRESULT hr = CoInitializeEx(nullptr, COINIT_APARTMENTTHREADED | COINIT_DISABLE_OLE1DDE);
    if (FAILED(hr)) {
        MessageBox(nullptr, L"COM初始化失败", L"错误", MB_OK | MB_ICONERROR);
        return -1;
    }

    // 初始化通用控件
    INITCOMMONCONTROLSEX icex;
    icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icex.dwICC = ICC_LISTVIEW_CLASSES | ICC_PROGRESS_CLASS | ICC_BAR_CLASSES;
    if (!InitCommonControlsEx(&icex)) {
        MessageBox(nullptr, L"通用控件初始化失败", L"错误", MB_OK | MB_ICONERROR);
        CoUninitialize();
        return -1;
    }

    // 设置全局变量
    g_hInstance = hInstance;

    // 创建应用程序实例
    g_pApp = new FileRenamerApp();
    if (!g_pApp) {
        MessageBox(nullptr, L"应用程序创建失败", L"错误", MB_OK | MB_ICONERROR);
        CoUninitialize();
        return -1;
    }

    int result = -1;

    // 初始化应用程序
    if (g_pApp->Initialize(hInstance)) {
        // 运行消息循环
        result = g_pApp->Run();
    }
    else {
        MessageBox(nullptr, L"应用程序初始化失败", L"错误", MB_OK | MB_ICONERROR);
    }

    // 清理
    if (g_pApp) {
        g_pApp->Shutdown();
        delete g_pApp;
        g_pApp = nullptr;
    }

    CoUninitialize();
    return result;
}
