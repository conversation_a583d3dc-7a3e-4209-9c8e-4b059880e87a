#pragma once

#include "main.h"
#include "FileManager.h"
#include "RenameEngine.h"
#include "UIManager.h"

// 主应用程序类
class FileRenamerApp {
public:
    FileRenamerApp();
    ~FileRenamerApp();

    // 应用程序生命周期
    bool Initialize(HINSTANCE hInstance);
    int Run();
    void Shutdown();

    // 窗口过程
    static LRESULT CALLBACK MainWndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam);
    LRESULT HandleMessage(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam);

    // 组件访问
    FileManager* GetFileManager() { return m_pFileManager.get(); }
    RenameEngine* GetRenameEngine() { return m_pRenameEngine.get(); }
    UIManager* GetUIManager() { return m_pUIManager.get(); }

    // 主要功能
    void SelectFiles();
    void SelectFolder();
    void ClearFileList();
    void GeneratePreview();
    void ExecuteRename();
    void UndoRename();

    // 状态管理
    void SetBusy(bool busy);
    bool IsBusy() const { return m_isBusy; }
    void UpdateStatus(const std::wstring& message);

private:
    HINSTANCE m_hInstance;
    HWND m_hMainWnd;
    HACCEL m_hAccelTable;
    
    // 组件
    std::unique_ptr<FileManager> m_pFileManager;
    std::unique_ptr<RenameEngine> m_pRenameEngine;
    std::unique_ptr<UIManager> m_pUIManager;
    
    // 状态
    bool m_isBusy;
    bool m_hasPreview;
    
    // 内部方法
    bool RegisterWindowClass();
    bool CreateMainWindow();
    void LoadAccelerators();
    void SetupDragDrop();
    
    // 消息处理
    void OnCreate();
    void OnDestroy();
    void OnSize(WPARAM wParam, LPARAM lParam);
    void OnCommand(WPARAM wParam, LPARAM lParam);
    void OnNotify(WPARAM wParam, LPARAM lParam);
    void OnDropFiles(WPARAM wParam, LPARAM lParam);
    void OnUpdatePreview();
    void OnUpdateStatus(LPARAM lParam);
    
    // 菜单处理
    void OnMenuFileExit();
    void OnMenuEditSelectAll();
    void OnMenuEditClearAll();
    void OnMenuHelpAbout();
    
    // 工具方法
    void EnableMenuItems(bool enabled);
    void SaveSettings();
    void LoadSettings();
};
