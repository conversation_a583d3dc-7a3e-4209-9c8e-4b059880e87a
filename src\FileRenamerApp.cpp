#include "FileRenamerApp.h"
#include "resource.h"

FileRenamerApp::FileRenamerApp()
    : m_hInstance(nullptr)
    , m_hMainWnd(nullptr)
    , m_hAccelTable(nullptr)
    , m_isBusy(false)
    , m_hasPreview(false)
{
}

FileRenamerApp::~FileRenamerApp()
{
}

bool FileRenamerApp::Initialize(HINSTANCE hInstance)
{
    m_hInstance = hInstance;

    // 注册窗口类
    if (!RegisterWindowClass()) {
        return false;
    }

    // 创建主窗口
    if (!CreateMainWindow()) {
        return false;
    }

    // 创建组件
    m_pFileManager = std::make_unique<FileManager>();
    m_pRenameEngine = std::make_unique<RenameEngine>();
    m_pUIManager = std::make_unique<UIManager>(m_hMainWnd);

    // 初始化UI
    if (!m_pUIManager->CreateControls()) {
        return false;
    }

    m_pUIManager->InitializeControls();
    
    // 加载加速键
    LoadAccelerators();
    
    // 设置拖拽支持
    SetupDragDrop();
    
    // 加载设置
    LoadSettings();

    // 显示窗口
    ShowWindow(m_hMainWnd, SW_SHOW);
    UpdateWindow(m_hMainWnd);

    return true;
}

int FileRenamerApp::Run()
{
    MSG msg;
    
    while (GetMessage(&msg, nullptr, 0, 0)) {
        if (!TranslateAccelerator(m_hMainWnd, m_hAccelTable, &msg)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }
    }

    return static_cast<int>(msg.wParam);
}

void FileRenamerApp::Shutdown()
{
    SaveSettings();
    
    if (m_hMainWnd) {
        DestroyWindow(m_hMainWnd);
        m_hMainWnd = nullptr;
    }
}

bool FileRenamerApp::RegisterWindowClass()
{
    WNDCLASSEXW wcex = {};
    wcex.cbSize = sizeof(WNDCLASSEX);
    wcex.style = CS_HREDRAW | CS_VREDRAW;
    wcex.lpfnWndProc = MainWndProc;
    wcex.cbClsExtra = 0;
    wcex.cbWndExtra = 0;
    wcex.hInstance = m_hInstance;
    wcex.hIcon = LoadIcon(m_hInstance, MAKEINTRESOURCE(IDI_MAIN_ICON));
    wcex.hCursor = LoadCursor(nullptr, IDC_ARROW);
    wcex.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wcex.lpszMenuName = MAKEINTRESOURCE(IDR_MAIN_MENU);
    wcex.lpszClassName = WINDOW_CLASS_NAME;
    wcex.hIconSm = LoadIcon(m_hInstance, MAKEINTRESOURCE(IDI_SMALL_ICON));

    return RegisterClassExW(&wcex) != 0;
}

bool FileRenamerApp::CreateMainWindow()
{
    m_hMainWnd = CreateWindowW(
        WINDOW_CLASS_NAME,
        APP_NAME,
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT,
        1000, 700,
        nullptr,
        nullptr,
        m_hInstance,
        this);

    return m_hMainWnd != nullptr;
}

void FileRenamerApp::LoadAccelerators()
{
    m_hAccelTable = LoadAccelerators(m_hInstance, MAKEINTRESOURCE(IDR_ACCELERATOR));
}

void FileRenamerApp::SetupDragDrop()
{
    DragAcceptFiles(m_hMainWnd, TRUE);
}

LRESULT CALLBACK FileRenamerApp::MainWndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam)
{
    FileRenamerApp* pApp = nullptr;

    if (message == WM_NCCREATE) {
        CREATESTRUCT* pCreate = reinterpret_cast<CREATESTRUCT*>(lParam);
        pApp = reinterpret_cast<FileRenamerApp*>(pCreate->lpCreateParams);
        SetWindowLongPtr(hWnd, GWLP_USERDATA, reinterpret_cast<LONG_PTR>(pApp));
    }
    else {
        pApp = reinterpret_cast<FileRenamerApp*>(GetWindowLongPtr(hWnd, GWLP_USERDATA));
    }

    if (pApp) {
        return pApp->HandleMessage(hWnd, message, wParam, lParam);
    }

    return DefWindowProc(hWnd, message, wParam, lParam);
}

LRESULT FileRenamerApp::HandleMessage(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam)
{
    switch (message) {
    case WM_CREATE:
        OnCreate();
        break;

    case WM_COMMAND:
        OnCommand(wParam, lParam);
        break;

    case WM_NOTIFY:
        OnNotify(wParam, lParam);
        break;

    case WM_SIZE:
        OnSize(wParam, lParam);
        break;

    case WM_DROPFILES:
        OnDropFiles(wParam, lParam);
        break;

    case WM_UPDATE_PREVIEW:
        OnUpdatePreview();
        break;

    case WM_UPDATE_STATUS:
        OnUpdateStatus(lParam);
        break;

    case WM_DESTROY:
        OnDestroy();
        break;

    default:
        return DefWindowProc(hWnd, message, wParam, lParam);
    }

    return 0;
}

void FileRenamerApp::OnCreate()
{
    // 窗口创建时的初始化工作在Initialize中完成
}

void FileRenamerApp::OnDestroy()
{
    PostQuitMessage(0);
}

void FileRenamerApp::OnSize(WPARAM wParam, LPARAM lParam)
{
    if (m_pUIManager && wParam != SIZE_MINIMIZED) {
        int width = LOWORD(lParam);
        int height = HIWORD(lParam);
        m_pUIManager->OnSize(width, height);
    }
}

void FileRenamerApp::OnCommand(WPARAM wParam, LPARAM lParam)
{
    int wmId = LOWORD(wParam);

    switch (wmId) {
    case ID_MENU_FILE_EXIT:
        OnMenuFileExit();
        break;

    case ID_MENU_EDIT_SELECT_ALL:
        OnMenuEditSelectAll();
        break;

    case ID_MENU_EDIT_CLEAR_ALL:
        OnMenuEditClearAll();
        break;

    case ID_MENU_HELP_ABOUT:
        OnMenuHelpAbout();
        break;

    case ID_BUTTON_SELECT_FILES:
        SelectFiles();
        break;

    case ID_BUTTON_SELECT_FOLDER:
        SelectFolder();
        break;

    case ID_BUTTON_CLEAR_LIST:
        ClearFileList();
        break;

    case ID_BUTTON_PREVIEW:
        GeneratePreview();
        break;

    case ID_BUTTON_EXECUTE:
        ExecuteRename();
        break;

    case ID_BUTTON_UNDO:
        UndoRename();
        break;

    default:
        if (m_pUIManager) {
            m_pUIManager->OnCommand(wParam, lParam);
        }
        break;
    }
}

void FileRenamerApp::OnNotify(WPARAM wParam, LPARAM lParam)
{
    if (m_pUIManager) {
        m_pUIManager->OnNotify(wParam, lParam);
    }
}

void FileRenamerApp::OnDropFiles(WPARAM wParam, LPARAM lParam)
{
    if (m_pUIManager) {
        m_pUIManager->OnDropFiles(reinterpret_cast<HDROP>(wParam));
    }
}

void FileRenamerApp::OnUpdatePreview()
{
    GeneratePreview();
}

void FileRenamerApp::OnUpdateStatus(LPARAM lParam)
{
    if (m_pUIManager) {
        std::wstring* pMessage = reinterpret_cast<std::wstring*>(lParam);
        if (pMessage) {
            m_pUIManager->SetStatusText(*pMessage);
            delete pMessage;
        }
    }
}

void FileRenamerApp::OnMenuFileExit()
{
    PostMessage(m_hMainWnd, WM_CLOSE, 0, 0);
}

void FileRenamerApp::OnMenuEditSelectAll()
{
    if (m_pFileManager) {
        m_pFileManager->SelectAll();
        if (m_pUIManager) {
            m_pUIManager->UpdateFileList(m_pFileManager->GetFiles());
        }
    }
}

void FileRenamerApp::OnMenuEditClearAll()
{
    ClearFileList();
}

void FileRenamerApp::OnMenuHelpAbout()
{
    if (m_pUIManager) {
        m_pUIManager->ShowAboutDialog();
    }
}

void FileRenamerApp::SelectFiles()
{
    if (m_isBusy || !m_pUIManager || !m_pFileManager) {
        return;
    }

    std::vector<std::wstring> selectedFiles;
    if (m_pUIManager->ShowSelectFilesDialog(selectedFiles)) {
        if (m_pFileManager->AddFiles(selectedFiles)) {
            m_pUIManager->UpdateFileList(m_pFileManager->GetFiles());
            UpdateStatus(L"已添加 " + std::to_wstring(selectedFiles.size()) + L" 个文件");
        }
    }
}

void FileRenamerApp::SelectFolder()
{
    if (m_isBusy || !m_pUIManager || !m_pFileManager) {
        return;
    }

    std::wstring selectedFolder;
    if (m_pUIManager->ShowSelectFolderDialog(selectedFolder)) {
        if (m_pFileManager->AddFolder(selectedFolder, false)) {
            m_pUIManager->UpdateFileList(m_pFileManager->GetFiles());
            UpdateStatus(L"已添加文件夹: " + selectedFolder);
        }
    }
}

void FileRenamerApp::ClearFileList()
{
    if (m_isBusy || !m_pFileManager || !m_pUIManager) {
        return;
    }

    if (m_pFileManager->GetFileCount() > 0) {
        if (m_pUIManager->ShowConfirmDialog(L"确定要清空文件列表吗？")) {
            m_pFileManager->ClearFiles();
            m_pUIManager->ClearFileLists();
            m_hasPreview = false;
            UpdateStatus(L"文件列表已清空");
        }
    }
}

void FileRenamerApp::GeneratePreview()
{
    if (m_isBusy || !m_pFileManager || !m_pRenameEngine || !m_pUIManager) {
        return;
    }

    if (m_pFileManager->GetFileCount() == 0) {
        m_pUIManager->ShowErrorDialog(L"请先选择要重命名的文件");
        return;
    }

    // 获取重命名规则
    RenameRule rule = m_pUIManager->GetRenameRule();
    m_pRenameEngine->SetRule(rule);

    // 验证规则
    if (!m_pRenameEngine->ValidateRule()) {
        m_pUIManager->ShowErrorDialog(L"重命名规则无效，请检查输入");
        return;
    }

    SetBusy(true);
    UpdateStatus(L"正在生成预览...");

    // 生成预览
    std::vector<FileInfo> previewFiles;
    if (m_pRenameEngine->GeneratePreview(m_pFileManager->GetFiles(), previewFiles)) {
        m_pUIManager->UpdatePreviewList(previewFiles);
        m_hasPreview = true;
        UpdateStatus(L"预览已生成");
    }
    else {
        m_pUIManager->ShowErrorDialog(L"预览生成失败");
        m_hasPreview = false;
    }

    SetBusy(false);
}

void FileRenamerApp::ExecuteRename()
{
    if (m_isBusy || !m_pFileManager || !m_pRenameEngine || !m_pUIManager) {
        return;
    }

    if (m_pFileManager->GetFileCount() == 0) {
        m_pUIManager->ShowErrorDialog(L"请先选择要重命名的文件");
        return;
    }

    if (!m_hasPreview) {
        m_pUIManager->ShowErrorDialog(L"请先生成预览");
        return;
    }

    // 确认执行
    if (!m_pUIManager->ShowConfirmDialog(L"确定要执行重命名操作吗？此操作不可撤销！")) {
        return;
    }

    SetBusy(true);
    UpdateStatus(L"正在执行重命名...");

    // 执行重命名
    std::vector<FileInfo> files = m_pFileManager->GetFiles();
    RenameResult result = m_pRenameEngine->ExecuteRename(files);

    if (result.success) {
        UpdateStatus(L"重命名完成，成功处理 " + std::to_wstring(result.processedCount) + L" 个文件");

        // 更新文件列表
        m_pFileManager->ClearFiles();
        m_pUIManager->ClearFileLists();
        m_hasPreview = false;
    }
    else {
        m_pUIManager->ShowErrorDialog(L"重命名失败: " + result.errorMessage);
    }

    SetBusy(false);
}

void FileRenamerApp::UndoRename()
{
    if (m_isBusy || !m_pRenameEngine || !m_pUIManager) {
        return;
    }

    if (!m_pRenameEngine->CanUndo()) {
        m_pUIManager->ShowErrorDialog(L"没有可撤销的操作");
        return;
    }

    if (!m_pUIManager->ShowConfirmDialog(L"确定要撤销上次重命名操作吗？")) {
        return;
    }

    SetBusy(true);
    UpdateStatus(L"正在撤销重命名...");

    if (m_pRenameEngine->UndoLastRename()) {
        UpdateStatus(L"撤销操作完成");
    }
    else {
        m_pUIManager->ShowErrorDialog(L"撤销操作失败");
    }

    SetBusy(false);
}

void FileRenamerApp::SetBusy(bool busy)
{
    m_isBusy = busy;
    if (m_pUIManager) {
        m_pUIManager->EnableControls(!busy);
    }
    EnableMenuItems(!busy);
}

void FileRenamerApp::UpdateStatus(const std::wstring& message)
{
    if (m_pUIManager) {
        m_pUIManager->SetStatusText(message);
    }
}

void FileRenamerApp::EnableMenuItems(bool enabled)
{
    HMENU hMenu = GetMenu(m_hMainWnd);
    if (hMenu) {
        EnableMenuItem(hMenu, ID_MENU_EDIT_SELECT_ALL, enabled ? MF_ENABLED : MF_GRAYED);
        EnableMenuItem(hMenu, ID_MENU_EDIT_CLEAR_ALL, enabled ? MF_ENABLED : MF_GRAYED);
    }
}

void FileRenamerApp::SaveSettings()
{
    // 保存窗口位置和大小
    RECT windowRect;
    if (GetWindowRect(m_hMainWnd, &windowRect)) {
        HKEY hKey;
        if (RegCreateKeyEx(HKEY_CURRENT_USER, L"Software\\FileRenamer", 0, nullptr,
                          REG_OPTION_NON_VOLATILE, KEY_WRITE, nullptr, &hKey, nullptr) == ERROR_SUCCESS) {

            DWORD left = windowRect.left;
            DWORD top = windowRect.top;
            DWORD width = windowRect.right - windowRect.left;
            DWORD height = windowRect.bottom - windowRect.top;

            RegSetValueEx(hKey, L"WindowLeft", 0, REG_DWORD, reinterpret_cast<BYTE*>(&left), sizeof(DWORD));
            RegSetValueEx(hKey, L"WindowTop", 0, REG_DWORD, reinterpret_cast<BYTE*>(&top), sizeof(DWORD));
            RegSetValueEx(hKey, L"WindowWidth", 0, REG_DWORD, reinterpret_cast<BYTE*>(&width), sizeof(DWORD));
            RegSetValueEx(hKey, L"WindowHeight", 0, REG_DWORD, reinterpret_cast<BYTE*>(&height), sizeof(DWORD));

            // 保存最后使用的重命名规则
            if (m_pUIManager) {
                RenameRule rule = m_pUIManager->GetRenameRule();

                RegSetValueEx(hKey, L"FindText", 0, REG_SZ,
                             reinterpret_cast<const BYTE*>(rule.findText.c_str()),
                             static_cast<DWORD>((rule.findText.length() + 1) * sizeof(wchar_t)));

                RegSetValueEx(hKey, L"ReplaceText", 0, REG_SZ,
                             reinterpret_cast<const BYTE*>(rule.replaceText.c_str()),
                             static_cast<DWORD>((rule.replaceText.length() + 1) * sizeof(wchar_t)));

                DWORD useRegex = rule.useRegex ? 1 : 0;
                DWORD addNumber = rule.addNumber ? 1 : 0;
                DWORD numberStart = rule.numberStart;
                DWORD numberStep = rule.numberStep;

                RegSetValueEx(hKey, L"UseRegex", 0, REG_DWORD, reinterpret_cast<BYTE*>(&useRegex), sizeof(DWORD));
                RegSetValueEx(hKey, L"AddNumber", 0, REG_DWORD, reinterpret_cast<BYTE*>(&addNumber), sizeof(DWORD));
                RegSetValueEx(hKey, L"NumberStart", 0, REG_DWORD, reinterpret_cast<BYTE*>(&numberStart), sizeof(DWORD));
                RegSetValueEx(hKey, L"NumberStep", 0, REG_DWORD, reinterpret_cast<BYTE*>(&numberStep), sizeof(DWORD));
            }

            RegCloseKey(hKey);
        }
    }
}

void FileRenamerApp::LoadSettings()
{
    HKEY hKey;
    if (RegOpenKeyEx(HKEY_CURRENT_USER, L"Software\\FileRenamer", 0, KEY_READ, &hKey) == ERROR_SUCCESS) {

        // 加载窗口位置和大小
        DWORD left = CW_USEDEFAULT, top = CW_USEDEFAULT;
        DWORD width = 1000, height = 700;
        DWORD dataSize = sizeof(DWORD);

        RegQueryValueEx(hKey, L"WindowLeft", nullptr, nullptr, reinterpret_cast<BYTE*>(&left), &dataSize);
        RegQueryValueEx(hKey, L"WindowTop", nullptr, nullptr, reinterpret_cast<BYTE*>(&top), &dataSize);
        RegQueryValueEx(hKey, L"WindowWidth", nullptr, nullptr, reinterpret_cast<BYTE*>(&width), &dataSize);
        RegQueryValueEx(hKey, L"WindowHeight", nullptr, nullptr, reinterpret_cast<BYTE*>(&height), &dataSize);

        // 应用窗口位置和大小
        if (left != CW_USEDEFAULT && top != CW_USEDEFAULT) {
            SetWindowPos(m_hMainWnd, nullptr, left, top, width, height, SWP_NOZORDER);
        }

        // 加载重命名规则
        if (m_pUIManager) {
            RenameRule rule;

            wchar_t buffer[1024];
            DWORD bufferSize = sizeof(buffer);

            if (RegQueryValueEx(hKey, L"FindText", nullptr, nullptr,
                               reinterpret_cast<BYTE*>(buffer), &bufferSize) == ERROR_SUCCESS) {
                rule.findText = buffer;
            }

            bufferSize = sizeof(buffer);
            if (RegQueryValueEx(hKey, L"ReplaceText", nullptr, nullptr,
                               reinterpret_cast<BYTE*>(buffer), &bufferSize) == ERROR_SUCCESS) {
                rule.replaceText = buffer;
            }

            DWORD value;
            dataSize = sizeof(DWORD);

            if (RegQueryValueEx(hKey, L"UseRegex", nullptr, nullptr,
                               reinterpret_cast<BYTE*>(&value), &dataSize) == ERROR_SUCCESS) {
                rule.useRegex = (value != 0);
            }

            if (RegQueryValueEx(hKey, L"AddNumber", nullptr, nullptr,
                               reinterpret_cast<BYTE*>(&value), &dataSize) == ERROR_SUCCESS) {
                rule.addNumber = (value != 0);
            }

            if (RegQueryValueEx(hKey, L"NumberStart", nullptr, nullptr,
                               reinterpret_cast<BYTE*>(&value), &dataSize) == ERROR_SUCCESS) {
                rule.numberStart = value;
            }

            if (RegQueryValueEx(hKey, L"NumberStep", nullptr, nullptr,
                               reinterpret_cast<BYTE*>(&value), &dataSize) == ERROR_SUCCESS) {
                rule.numberStep = value;
            }

            m_pUIManager->SetRenameRule(rule);
        }

        RegCloseKey(hKey);
    }
}
