#pragma once

#include "main.h"
#include "FileManager.h"
#include "RenameEngine.h"

// UI管理器类
class UIManager {
public:
    UIManager(HWND hMainWnd);
    ~UIManager();

    // 窗口创建和初始化
    bool CreateControls();
    void InitializeControls();
    void SetupLayout();

    // 事件处理
    void OnCommand(WPARAM wParam, LPARAM lParam);
    void OnNotify(WPARAM wParam, LPARAM lParam);
    void OnSize(int width, int height);
    void OnDropFiles(HDROP hDrop);

    // 文件列表管理
    void UpdateFileList(const std::vector<FileInfo>& files);
    void UpdatePreviewList(const std::vector<FileInfo>& previewFiles);
    void ClearFileLists();
    
    // 控件操作
    void EnableControls(bool enabled);
    void UpdateProgress(int current, int total);
    void SetStatusText(const std::wstring& text);
    
    // 获取用户输入
    RenameRule GetRenameRule() const;
    void SetRenameRule(const RenameRule& rule);
    
    // 对话框
    bool ShowSelectFilesDialog(std::vector<std::wstring>& selectedFiles);
    bool ShowSelectFolderDialog(std::wstring& selectedFolder);
    void ShowAboutDialog();
    bool ShowConfirmDialog(const std::wstring& message);
    void ShowErrorDialog(const std::wstring& message);

private:
    HWND m_hMainWnd;
    
    // 控件句柄
    HWND m_hFileListView;
    HWND m_hPreviewListView;
    HWND m_hFindEdit;
    HWND m_hReplaceEdit;
    HWND m_hRegexCheck;
    HWND m_hNumberCheck;
    HWND m_hNumberStartEdit;
    HWND m_hNumberStepEdit;
    HWND m_hSelectFilesBtn;
    HWND m_hSelectFolderBtn;
    HWND m_hClearBtn;
    HWND m_hPreviewBtn;
    HWND m_hExecuteBtn;
    HWND m_hUndoBtn;
    HWND m_hStatusBar;
    HWND m_hProgressBar;
    HWND m_hRegexTemplateCombo;
    HWND m_hRegexHelpBtn;
    
    // 布局相关
    int m_windowWidth;
    int m_windowHeight;
    
    // 内部方法
    void CreateFileListView();
    void CreatePreviewListView();
    void CreateInputControls();
    void CreateButtons();
    void CreateStatusBar();
    
    void SetupFileListColumns();
    void SetupPreviewListColumns();
    
    void ResizeControls();
    void UpdateListViewItem(HWND hListView, int index, const FileInfo& file, bool isPreview = false);
    
    // 事件处理辅助方法
    void OnSelectFiles();
    void OnSelectFolder();
    void OnClearList();
    void OnPreview();
    void OnExecute();
    void OnUndo();
    void OnFileListSelectionChanged();
    void OnRegexTemplateSelected();
    void OnRegexHelp();

    // 正则表达式模板相关
    void InitializeRegexTemplates();
    void ShowRegexHelpDialog();
    
    // 工具方法
    std::wstring FormatFileSize(LARGE_INTEGER size);
    std::wstring FormatFileTime(FILETIME time);
    void SetListViewItemText(HWND hListView, int item, int subItem, const std::wstring& text);
};
