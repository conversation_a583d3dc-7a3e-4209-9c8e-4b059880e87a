#include "UIManager.h"
#include "resource.h"
#include <sstream>
#include <iomanip>

UIManager::UIManager(HWND hMainWnd)
    : m_hMainWnd(hMainWnd)
    , m_hFileListView(nullptr)
    , m_hPreviewListView(nullptr)
    , m_hFindEdit(nullptr)
    , m_hReplaceEdit(nullptr)
    , m_hRegexCheck(nullptr)
    , m_hNumberCheck(nullptr)
    , m_hNumberStartEdit(nullptr)
    , m_hNumberStepEdit(nullptr)
    , m_hSelectFilesBtn(nullptr)
    , m_hSelectFolderBtn(nullptr)
    , m_hClearBtn(nullptr)
    , m_hPreviewBtn(nullptr)
    , m_hExecuteBtn(nullptr)
    , m_hUndoBtn(nullptr)
    , m_hStatusBar(nullptr)
    , m_hProgressBar(nullptr)
    , m_windowWidth(1000)
    , m_windowHeight(700)
{
}

UIManager::~UIManager()
{
}

bool UIManager::CreateControls()
{
    // 创建状态栏
    CreateStatusBar();
    
    // 创建文件列表视图
    CreateFileListView();
    
    // 创建预览列表视图
    CreatePreviewListView();
    
    // 创建输入控件
    CreateInputControls();
    
    // 创建按钮
    CreateButtons();
    
    return true;
}

void UIManager::InitializeControls()
{
    // 设置列表视图列
    SetupFileListColumns();
    SetupPreviewListColumns();

    // 初始化正则表达式模板
    InitializeRegexTemplates();

    // 设置初始布局
    SetupLayout();
}

void UIManager::CreateStatusBar()
{
    m_hStatusBar = CreateWindowEx(
        0,
        STATUSCLASSNAME,
        nullptr,
        WS_CHILD | WS_VISIBLE | SBARS_SIZEGRIP,
        0, 0, 0, 0,
        m_hMainWnd,
        reinterpret_cast<HMENU>(ID_STATUSBAR),
        GetModuleHandle(nullptr),
        nullptr);

    if (m_hStatusBar) {
        // 设置状态栏分区：状态文本 | 进度条 | 调整大小手柄
        int parts[] = { 300, 500, -1 };
        SendMessage(m_hStatusBar, SB_SETPARTS, 3, reinterpret_cast<LPARAM>(parts));
        SetStatusText(L"就绪");

        // 在状态栏中创建进度条
        RECT progressRect;
        SendMessage(m_hStatusBar, SB_GETRECT, 1, reinterpret_cast<LPARAM>(&progressRect));

        m_hProgressBar = CreateWindowEx(
            0,
            PROGRESS_CLASS,
            nullptr,
            WS_CHILD | WS_VISIBLE,
            progressRect.left + 2,
            progressRect.top + 2,
            progressRect.right - progressRect.left - 4,
            progressRect.bottom - progressRect.top - 4,
            m_hStatusBar,
            reinterpret_cast<HMENU>(ID_PROGRESSBAR),
            GetModuleHandle(nullptr),
            nullptr);

        // 初始时隐藏进度条
        ShowWindow(m_hProgressBar, SW_HIDE);
    }
}

void UIManager::CreateFileListView()
{
    m_hFileListView = CreateWindowEx(
        WS_EX_CLIENTEDGE,
        WC_LISTVIEW,
        L"",
        WS_CHILD | WS_VISIBLE | LVS_REPORT | LVS_SHOWSELALWAYS | LVS_SINGLESEL,
        10, 10, 480, 300,
        m_hMainWnd,
        reinterpret_cast<HMENU>(ID_LISTVIEW_FILES),
        GetModuleHandle(nullptr),
        nullptr);

    if (m_hFileListView) {
        // 设置扩展样式
        ListView_SetExtendedListViewStyle(m_hFileListView, 
            LVS_EX_FULLROWSELECT | LVS_EX_GRIDLINES | LVS_EX_CHECKBOXES);
    }
}

void UIManager::CreatePreviewListView()
{
    m_hPreviewListView = CreateWindowEx(
        WS_EX_CLIENTEDGE,
        WC_LISTVIEW,
        L"",
        WS_CHILD | WS_VISIBLE | LVS_REPORT | LVS_SHOWSELALWAYS | LVS_SINGLESEL,
        500, 10, 480, 300,
        m_hMainWnd,
        reinterpret_cast<HMENU>(ID_LISTVIEW_PREVIEW),
        GetModuleHandle(nullptr),
        nullptr);

    if (m_hPreviewListView) {
        // 设置扩展样式
        ListView_SetExtendedListViewStyle(m_hPreviewListView, 
            LVS_EX_FULLROWSELECT | LVS_EX_GRIDLINES);
    }
}

void UIManager::CreateInputControls()
{
    // 查找文本标签和编辑框
    CreateWindow(L"STATIC", L"查找文本:", WS_CHILD | WS_VISIBLE,
        10, 330, 80, 20, m_hMainWnd, nullptr, GetModuleHandle(nullptr), nullptr);
    
    m_hFindEdit = CreateWindowEx(WS_EX_CLIENTEDGE, L"EDIT", L"",
        WS_CHILD | WS_VISIBLE | ES_AUTOHSCROLL,
        100, 328, 200, 24, m_hMainWnd, reinterpret_cast<HMENU>(ID_EDIT_FIND_TEXT),
        GetModuleHandle(nullptr), nullptr);

    // 替换文本标签和编辑框
    CreateWindow(L"STATIC", L"替换为:", WS_CHILD | WS_VISIBLE,
        320, 330, 80, 20, m_hMainWnd, nullptr, GetModuleHandle(nullptr), nullptr);
    
    m_hReplaceEdit = CreateWindowEx(WS_EX_CLIENTEDGE, L"EDIT", L"",
        WS_CHILD | WS_VISIBLE | ES_AUTOHSCROLL,
        400, 328, 200, 24, m_hMainWnd, reinterpret_cast<HMENU>(ID_EDIT_REPLACE_TEXT),
        GetModuleHandle(nullptr), nullptr);

    // 正则表达式复选框
    m_hRegexCheck = CreateWindow(L"BUTTON", L"使用正则表达式",
        WS_CHILD | WS_VISIBLE | BS_AUTOCHECKBOX,
        620, 330, 120, 20, m_hMainWnd, reinterpret_cast<HMENU>(ID_CHECKBOX_USE_REGEX),
        GetModuleHandle(nullptr), nullptr);

    // 正则表达式模板下拉框
    CreateWindow(L"STATIC", L"模板:", WS_CHILD | WS_VISIBLE,
        750, 332, 40, 20, m_hMainWnd, nullptr, GetModuleHandle(nullptr), nullptr);

    m_hRegexTemplateCombo = CreateWindow(L"COMBOBOX", L"",
        WS_CHILD | WS_VISIBLE | CBS_DROPDOWNLIST | WS_VSCROLL,
        790, 328, 150, 200, m_hMainWnd, reinterpret_cast<HMENU>(ID_COMBO_REGEX_TEMPLATES),
        GetModuleHandle(nullptr), nullptr);

    // 正则表达式帮助按钮
    m_hRegexHelpBtn = CreateWindow(L"BUTTON", L"?",
        WS_CHILD | WS_VISIBLE | BS_PUSHBUTTON,
        950, 328, 24, 24, m_hMainWnd, reinterpret_cast<HMENU>(ID_BUTTON_REGEX_HELP),
        GetModuleHandle(nullptr), nullptr);

    // 添加序号复选框
    m_hNumberCheck = CreateWindow(L"BUTTON", L"添加序号",
        WS_CHILD | WS_VISIBLE | BS_AUTOCHECKBOX,
        10, 360, 80, 20, m_hMainWnd, reinterpret_cast<HMENU>(ID_CHECKBOX_ADD_NUMBER),
        GetModuleHandle(nullptr), nullptr);

    // 序号起始值
    CreateWindow(L"STATIC", L"起始值:", WS_CHILD | WS_VISIBLE,
        100, 362, 50, 20, m_hMainWnd, nullptr, GetModuleHandle(nullptr), nullptr);
    
    m_hNumberStartEdit = CreateWindowEx(WS_EX_CLIENTEDGE, L"EDIT", L"1",
        WS_CHILD | WS_VISIBLE | ES_AUTOHSCROLL | ES_NUMBER,
        150, 360, 50, 24, m_hMainWnd, reinterpret_cast<HMENU>(ID_EDIT_NUMBER_START),
        GetModuleHandle(nullptr), nullptr);

    // 序号步长
    CreateWindow(L"STATIC", L"步长:", WS_CHILD | WS_VISIBLE,
        220, 362, 40, 20, m_hMainWnd, nullptr, GetModuleHandle(nullptr), nullptr);
    
    m_hNumberStepEdit = CreateWindowEx(WS_EX_CLIENTEDGE, L"EDIT", L"1",
        WS_CHILD | WS_VISIBLE | ES_AUTOHSCROLL | ES_NUMBER,
        260, 360, 50, 24, m_hMainWnd, reinterpret_cast<HMENU>(ID_EDIT_NUMBER_STEP),
        GetModuleHandle(nullptr), nullptr);
}

void UIManager::CreateButtons()
{
    // 选择文件按钮
    m_hSelectFilesBtn = CreateWindow(L"BUTTON", L"选择文件",
        WS_CHILD | WS_VISIBLE | BS_PUSHBUTTON,
        10, 400, 80, 30, m_hMainWnd, reinterpret_cast<HMENU>(ID_BUTTON_SELECT_FILES),
        GetModuleHandle(nullptr), nullptr);

    // 选择文件夹按钮
    m_hSelectFolderBtn = CreateWindow(L"BUTTON", L"选择文件夹",
        WS_CHILD | WS_VISIBLE | BS_PUSHBUTTON,
        100, 400, 80, 30, m_hMainWnd, reinterpret_cast<HMENU>(ID_BUTTON_SELECT_FOLDER),
        GetModuleHandle(nullptr), nullptr);

    // 清空列表按钮
    m_hClearBtn = CreateWindow(L"BUTTON", L"清空列表",
        WS_CHILD | WS_VISIBLE | BS_PUSHBUTTON,
        190, 400, 80, 30, m_hMainWnd, reinterpret_cast<HMENU>(ID_BUTTON_CLEAR_LIST),
        GetModuleHandle(nullptr), nullptr);

    // 预览按钮
    m_hPreviewBtn = CreateWindow(L"BUTTON", L"预览 (F5)",
        WS_CHILD | WS_VISIBLE | BS_PUSHBUTTON,
        300, 400, 80, 30, m_hMainWnd, reinterpret_cast<HMENU>(ID_BUTTON_PREVIEW),
        GetModuleHandle(nullptr), nullptr);

    // 执行按钮
    m_hExecuteBtn = CreateWindow(L"BUTTON", L"执行 (F9)",
        WS_CHILD | WS_VISIBLE | BS_PUSHBUTTON,
        390, 400, 80, 30, m_hMainWnd, reinterpret_cast<HMENU>(ID_BUTTON_EXECUTE),
        GetModuleHandle(nullptr), nullptr);

    // 撤销按钮
    m_hUndoBtn = CreateWindow(L"BUTTON", L"撤销",
        WS_CHILD | WS_VISIBLE | BS_PUSHBUTTON,
        480, 400, 80, 30, m_hMainWnd, reinterpret_cast<HMENU>(ID_BUTTON_UNDO),
        GetModuleHandle(nullptr), nullptr);
}

void UIManager::SetupFileListColumns()
{
    if (!m_hFileListView) return;

    LVCOLUMN lvc = {};
    lvc.mask = LVCF_TEXT | LVCF_WIDTH | LVCF_SUBITEM;

    // 文件名列
    lvc.iSubItem = 0;
    lvc.pszText = const_cast<LPWSTR>(L"文件名");
    lvc.cx = 200;
    ListView_InsertColumn(m_hFileListView, 0, &lvc);

    // 路径列
    lvc.iSubItem = 1;
    lvc.pszText = const_cast<LPWSTR>(L"路径");
    lvc.cx = 180;
    ListView_InsertColumn(m_hFileListView, 1, &lvc);

    // 大小列
    lvc.iSubItem = 2;
    lvc.pszText = const_cast<LPWSTR>(L"大小");
    lvc.cx = 80;
    ListView_InsertColumn(m_hFileListView, 2, &lvc);

    // 修改日期列
    lvc.iSubItem = 3;
    lvc.pszText = const_cast<LPWSTR>(L"修改日期");
    lvc.cx = 120;
    ListView_InsertColumn(m_hFileListView, 3, &lvc);
}

void UIManager::SetupPreviewListColumns()
{
    if (!m_hPreviewListView) return;

    LVCOLUMN lvc = {};
    lvc.mask = LVCF_TEXT | LVCF_WIDTH | LVCF_SUBITEM;

    // 原始名称列
    lvc.iSubItem = 0;
    lvc.pszText = const_cast<LPWSTR>(L"原始名称");
    lvc.cx = 200;
    ListView_InsertColumn(m_hPreviewListView, 0, &lvc);

    // 新名称列
    lvc.iSubItem = 1;
    lvc.pszText = const_cast<LPWSTR>(L"新名称");
    lvc.cx = 200;
    ListView_InsertColumn(m_hPreviewListView, 1, &lvc);

    // 路径列
    lvc.iSubItem = 2;
    lvc.pszText = const_cast<LPWSTR>(L"路径");
    lvc.cx = 180;
    ListView_InsertColumn(m_hPreviewListView, 2, &lvc);
}

void UIManager::SetupLayout()
{
    RECT clientRect;
    GetClientRect(m_hMainWnd, &clientRect);
    m_windowWidth = clientRect.right - clientRect.left;
    m_windowHeight = clientRect.bottom - clientRect.top;

    ResizeControls();
}

void UIManager::OnSize(int width, int height)
{
    m_windowWidth = width;
    m_windowHeight = height;
    ResizeControls();
}

void UIManager::ResizeControls()
{
    if (!m_hMainWnd) return;

    // 状态栏自动调整
    if (m_hStatusBar) {
        SendMessage(m_hStatusBar, WM_SIZE, 0, 0);
    }

    // 获取状态栏高度
    RECT statusRect = {};
    if (m_hStatusBar) {
        GetWindowRect(m_hStatusBar, &statusRect);
    }
    int statusHeight = statusRect.bottom - statusRect.top;

    // 计算可用区域
    int availableHeight = m_windowHeight - statusHeight - 20;
    int listHeight = (availableHeight - 120) / 2; // 为控件和按钮留出空间
    int listWidth = (m_windowWidth - 30) / 2;

    // 调整文件列表视图
    if (m_hFileListView) {
        SetWindowPos(m_hFileListView, nullptr, 10, 10, listWidth, listHeight,
            SWP_NOZORDER);
    }

    // 调整预览列表视图
    if (m_hPreviewListView) {
        SetWindowPos(m_hPreviewListView, nullptr, listWidth + 20, 10, listWidth, listHeight,
            SWP_NOZORDER);
    }

    // 调整输入控件位置
    int inputY = listHeight + 20;

    // 查找文本相关控件保持原位置，只调整Y坐标
    HWND hFindLabel = GetDlgItem(m_hMainWnd, -1); // 静态控件没有ID，需要特殊处理

    if (m_hFindEdit) {
        SetWindowPos(m_hFindEdit, nullptr, 100, inputY, 200, 24, SWP_NOZORDER);
    }

    if (m_hReplaceEdit) {
        SetWindowPos(m_hReplaceEdit, nullptr, 400, inputY, 200, 24, SWP_NOZORDER);
    }

    if (m_hRegexCheck) {
        SetWindowPos(m_hRegexCheck, nullptr, 620, inputY + 2, 120, 20, SWP_NOZORDER);
    }

    // 序号相关控件
    inputY += 30;
    if (m_hNumberCheck) {
        SetWindowPos(m_hNumberCheck, nullptr, 10, inputY, 80, 20, SWP_NOZORDER);
    }

    if (m_hNumberStartEdit) {
        SetWindowPos(m_hNumberStartEdit, nullptr, 150, inputY, 50, 24, SWP_NOZORDER);
    }

    if (m_hNumberStepEdit) {
        SetWindowPos(m_hNumberStepEdit, nullptr, 260, inputY, 50, 24, SWP_NOZORDER);
    }

    // 按钮
    inputY += 40;
    int buttonSpacing = 90;
    int buttonX = 10;

    if (m_hSelectFilesBtn) {
        SetWindowPos(m_hSelectFilesBtn, nullptr, buttonX, inputY, 80, 30, SWP_NOZORDER);
        buttonX += buttonSpacing;
    }

    if (m_hSelectFolderBtn) {
        SetWindowPos(m_hSelectFolderBtn, nullptr, buttonX, inputY, 80, 30, SWP_NOZORDER);
        buttonX += buttonSpacing;
    }

    if (m_hClearBtn) {
        SetWindowPos(m_hClearBtn, nullptr, buttonX, inputY, 80, 30, SWP_NOZORDER);
        buttonX += buttonSpacing;
    }

    if (m_hPreviewBtn) {
        SetWindowPos(m_hPreviewBtn, nullptr, buttonX, inputY, 80, 30, SWP_NOZORDER);
        buttonX += buttonSpacing;
    }

    if (m_hExecuteBtn) {
        SetWindowPos(m_hExecuteBtn, nullptr, buttonX, inputY, 80, 30, SWP_NOZORDER);
        buttonX += buttonSpacing;
    }

    if (m_hUndoBtn) {
        SetWindowPos(m_hUndoBtn, nullptr, buttonX, inputY, 80, 30, SWP_NOZORDER);
    }
}

void UIManager::UpdateFileList(const std::vector<FileInfo>& files)
{
    if (!m_hFileListView) return;

    // 清空现有项目
    ListView_DeleteAllItems(m_hFileListView);

    // 添加文件项目
    for (size_t i = 0; i < files.size(); ++i) {
        UpdateListViewItem(m_hFileListView, static_cast<int>(i), files[i], false);
    }
}

void UIManager::UpdatePreviewList(const std::vector<FileInfo>& previewFiles)
{
    if (!m_hPreviewListView) return;

    // 清空现有项目
    ListView_DeleteAllItems(m_hPreviewListView);

    // 添加预览项目
    for (size_t i = 0; i < previewFiles.size(); ++i) {
        UpdateListViewItem(m_hPreviewListView, static_cast<int>(i), previewFiles[i], true);
    }
}

void UIManager::ClearFileLists()
{
    if (m_hFileListView) {
        ListView_DeleteAllItems(m_hFileListView);
    }
    if (m_hPreviewListView) {
        ListView_DeleteAllItems(m_hPreviewListView);
    }
}

void UIManager::UpdateListViewItem(HWND hListView, int index, const FileInfo& file, bool isPreview)
{
    LVITEM lvi = {};
    lvi.mask = LVIF_TEXT | LVIF_PARAM;
    lvi.iItem = index;
    lvi.iSubItem = 0;
    lvi.lParam = reinterpret_cast<LPARAM>(&file);

    if (isPreview) {
        // 预览列表：原始名称、新名称、路径
        lvi.pszText = const_cast<LPWSTR>(file.originalName.c_str());
        ListView_InsertItem(hListView, &lvi);

        SetListViewItemText(hListView, index, 1, file.newName);
        SetListViewItemText(hListView, index, 2, file.directory);
    }
    else {
        // 文件列表：文件名、路径、大小、修改日期
        lvi.pszText = const_cast<LPWSTR>(file.originalName.c_str());
        ListView_InsertItem(hListView, &lvi);

        SetListViewItemText(hListView, index, 1, file.directory);
        SetListViewItemText(hListView, index, 2, FormatFileSize(file.fileSize));
        SetListViewItemText(hListView, index, 3, FormatFileTime(file.lastWriteTime));

        // 设置复选框状态
        ListView_SetCheckState(hListView, index, file.isSelected);
    }
}

void UIManager::SetListViewItemText(HWND hListView, int item, int subItem, const std::wstring& text)
{
    LVITEM lvi = {};
    lvi.mask = LVIF_TEXT;
    lvi.iItem = item;
    lvi.iSubItem = subItem;
    lvi.pszText = const_cast<LPWSTR>(text.c_str());
    ListView_SetItem(hListView, &lvi);
}

std::wstring UIManager::FormatFileSize(LARGE_INTEGER size)
{
    const wchar_t* units[] = { L"B", L"KB", L"MB", L"GB", L"TB" };
    double fileSize = static_cast<double>(size.QuadPart);
    int unitIndex = 0;

    while (fileSize >= 1024.0 && unitIndex < 4) {
        fileSize /= 1024.0;
        unitIndex++;
    }

    std::wostringstream oss;
    oss << std::fixed << std::setprecision(1) << fileSize << L" " << units[unitIndex];
    return oss.str();
}

std::wstring UIManager::FormatFileTime(FILETIME time)
{
    SYSTEMTIME st;
    if (!FileTimeToSystemTime(&time, &st)) {
        return L"";
    }

    std::wostringstream oss;
    oss << std::setfill(L'0') << std::setw(4) << st.wYear << L"-"
        << std::setw(2) << st.wMonth << L"-"
        << std::setw(2) << st.wDay << L" "
        << std::setw(2) << st.wHour << L":"
        << std::setw(2) << st.wMinute;
    return oss.str();
}

bool UIManager::ShowSelectFilesDialog(std::vector<std::wstring>& selectedFiles)
{
    selectedFiles.clear();

    OPENFILENAME ofn = {};
    wchar_t szFile[32768] = {}; // 支持多选需要大缓冲区

    ofn.lStructSize = sizeof(ofn);
    ofn.hwndOwner = m_hMainWnd;
    ofn.lpstrFile = szFile;
    ofn.nMaxFile = sizeof(szFile) / sizeof(wchar_t);
    ofn.lpstrFilter = L"所有文件\0*.*\0文本文件\0*.txt\0图片文件\0*.jpg;*.jpeg;*.png;*.bmp;*.gif\0";
    ofn.nFilterIndex = 1;
    ofn.lpstrFileTitle = nullptr;
    ofn.nMaxFileTitle = 0;
    ofn.lpstrInitialDir = nullptr;
    ofn.lpstrTitle = L"选择要重命名的文件";
    ofn.Flags = OFN_PATHMUSTEXIST | OFN_FILEMUSTEXIST | OFN_ALLOWMULTISELECT | OFN_EXPLORER;

    if (GetOpenFileName(&ofn)) {
        // 解析多选文件
        std::wstring directory = szFile;
        wchar_t* fileName = szFile + directory.length() + 1;

        if (*fileName == L'\0') {
            // 只选择了一个文件
            selectedFiles.push_back(directory);
        }
        else {
            // 选择了多个文件
            while (*fileName) {
                std::wstring fullPath = directory + L"\\" + fileName;
                selectedFiles.push_back(fullPath);
                fileName += wcslen(fileName) + 1;
            }
        }
        return true;
    }

    return false;
}

bool UIManager::ShowSelectFolderDialog(std::wstring& selectedFolder)
{
    selectedFolder.clear();

    BROWSEINFO bi = {};
    bi.hwndOwner = m_hMainWnd;
    bi.lpszTitle = L"选择要处理的文件夹";
    bi.ulFlags = BIF_RETURNONLYFSDIRS | BIF_NEWDIALOGSTYLE;

    LPITEMIDLIST pidl = SHBrowseForFolder(&bi);
    if (pidl) {
        wchar_t szPath[MAX_PATH];
        if (SHGetPathFromIDList(pidl, szPath)) {
            selectedFolder = szPath;
        }
        CoTaskMemFree(pidl);
        return !selectedFolder.empty();
    }

    return false;
}

void UIManager::ShowAboutDialog()
{
    DialogBox(GetModuleHandle(nullptr), MAKEINTRESOURCE(IDD_ABOUT_DIALOG),
              m_hMainWnd, nullptr);
}

bool UIManager::ShowConfirmDialog(const std::wstring& message)
{
    int result = MessageBox(m_hMainWnd, message.c_str(), L"确认",
                           MB_YESNO | MB_ICONQUESTION);
    return result == IDYES;
}

void UIManager::ShowErrorDialog(const std::wstring& message)
{
    MessageBox(m_hMainWnd, message.c_str(), L"错误", MB_OK | MB_ICONERROR);
}

void UIManager::EnableControls(bool enabled)
{
    EnableWindow(m_hSelectFilesBtn, enabled);
    EnableWindow(m_hSelectFolderBtn, enabled);
    EnableWindow(m_hClearBtn, enabled);
    EnableWindow(m_hPreviewBtn, enabled);
    EnableWindow(m_hExecuteBtn, enabled);
    EnableWindow(m_hUndoBtn, enabled);
    EnableWindow(m_hFindEdit, enabled);
    EnableWindow(m_hReplaceEdit, enabled);
    EnableWindow(m_hRegexCheck, enabled);
    EnableWindow(m_hNumberCheck, enabled);
    EnableWindow(m_hNumberStartEdit, enabled);
    EnableWindow(m_hNumberStepEdit, enabled);
}

void UIManager::SetStatusText(const std::wstring& text)
{
    if (m_hStatusBar) {
        SendMessage(m_hStatusBar, SB_SETTEXT, 0, reinterpret_cast<LPARAM>(text.c_str()));
    }
}

void UIManager::UpdateProgress(int current, int total)
{
    if (m_hProgressBar) {
        if (total > 0) {
            // 显示进度条
            ShowWindow(m_hProgressBar, SW_SHOW);
            SendMessage(m_hProgressBar, PBM_SETRANGE, 0, MAKELPARAM(0, total));
            SendMessage(m_hProgressBar, PBM_SETPOS, current, 0);

            // 更新状态栏中的进度文本
            std::wstring progressText = L"进度: " + std::to_wstring(current) +
                                       L"/" + std::to_wstring(total);
            SendMessage(m_hStatusBar, SB_SETTEXT, 1, reinterpret_cast<LPARAM>(progressText.c_str()));
        }
        else {
            // 隐藏进度条
            ShowWindow(m_hProgressBar, SW_HIDE);
            SendMessage(m_hStatusBar, SB_SETTEXT, 1, reinterpret_cast<LPARAM>(L""));
        }
    }
}

RenameRule UIManager::GetRenameRule() const
{
    RenameRule rule;

    // 获取查找和替换文本
    if (m_hFindEdit) {
        wchar_t buffer[1024];
        GetWindowText(m_hFindEdit, buffer, sizeof(buffer) / sizeof(wchar_t));
        rule.findText = buffer;
    }

    if (m_hReplaceEdit) {
        wchar_t buffer[1024];
        GetWindowText(m_hReplaceEdit, buffer, sizeof(buffer) / sizeof(wchar_t));
        rule.replaceText = buffer;
    }

    // 获取复选框状态
    if (m_hRegexCheck) {
        rule.useRegex = (SendMessage(m_hRegexCheck, BM_GETCHECK, 0, 0) == BST_CHECKED);
    }

    if (m_hNumberCheck) {
        rule.addNumber = (SendMessage(m_hNumberCheck, BM_GETCHECK, 0, 0) == BST_CHECKED);
    }

    // 获取序号设置
    if (m_hNumberStartEdit) {
        wchar_t buffer[32];
        GetWindowText(m_hNumberStartEdit, buffer, sizeof(buffer) / sizeof(wchar_t));
        rule.numberStart = _wtoi(buffer);
    }

    if (m_hNumberStepEdit) {
        wchar_t buffer[32];
        GetWindowText(m_hNumberStepEdit, buffer, sizeof(buffer) / sizeof(wchar_t));
        rule.numberStep = _wtoi(buffer);
    }

    // 设置规则类型
    if (rule.useRegex) {
        rule.type = RenameRuleType::REGEX_REPLACE;
    }
    else if (!rule.findText.empty() || !rule.replaceText.empty()) {
        rule.type = RenameRuleType::SIMPLE_REPLACE;
    }
    else if (rule.addNumber) {
        rule.type = RenameRuleType::ADD_NUMBER;
    }

    return rule;
}

void UIManager::SetRenameRule(const RenameRule& rule)
{
    if (m_hFindEdit) {
        SetWindowText(m_hFindEdit, rule.findText.c_str());
    }

    if (m_hReplaceEdit) {
        SetWindowText(m_hReplaceEdit, rule.replaceText.c_str());
    }

    if (m_hRegexCheck) {
        SendMessage(m_hRegexCheck, BM_SETCHECK, rule.useRegex ? BST_CHECKED : BST_UNCHECKED, 0);
    }

    if (m_hNumberCheck) {
        SendMessage(m_hNumberCheck, BM_SETCHECK, rule.addNumber ? BST_CHECKED : BST_UNCHECKED, 0);
    }

    if (m_hNumberStartEdit) {
        SetWindowText(m_hNumberStartEdit, std::to_wstring(rule.numberStart).c_str());
    }

    if (m_hNumberStepEdit) {
        SetWindowText(m_hNumberStepEdit, std::to_wstring(rule.numberStep).c_str());
    }
}

void UIManager::InitializeRegexTemplates()
{
    if (!m_hRegexTemplateCombo) return;

    // 添加常用正则表达式模板
    struct RegexTemplate {
        const wchar_t* name;
        const wchar_t* pattern;
        const wchar_t* description;
    };

    const RegexTemplate templates[] = {
        { L"选择模板...", L"", L"请选择一个正则表达式模板" },
        { L"移除数字", L"\\d+", L"匹配所有数字" },
        { L"移除空格", L"\\s+", L"匹配所有空白字符" },
        { L"移除括号内容", L"\\([^)]*\\)", L"匹配括号及其内容" },
        { L"移除方括号内容", L"\\[[^\\]]*\\]", L"匹配方括号及其内容" },
        { L"移除特殊字符", L"[^\\w\\s\\.]", L"匹配除字母、数字、空格、点之外的字符" },
        { L"提取文件名", L"^(.+)\\.[^.]+$", L"提取不含扩展名的文件名" },
        { L"匹配日期(YYYY-MM-DD)", L"\\d{4}-\\d{2}-\\d{2}", L"匹配日期格式" },
        { L"匹配时间(HH:MM:SS)", L"\\d{2}:\\d{2}:\\d{2}", L"匹配时间格式" },
        { L"移除前导零", L"^0+", L"移除开头的零" },
        { L"匹配版本号", L"v?\\d+\\.\\d+(\\.\\d+)?", L"匹配版本号格式" }
    };

    for (const auto& tmpl : templates) {
        int index = static_cast<int>(SendMessage(m_hRegexTemplateCombo, CB_ADDSTRING, 0,
                                               reinterpret_cast<LPARAM>(tmpl.name)));
        SendMessage(m_hRegexTemplateCombo, CB_SETITEMDATA, index,
                   reinterpret_cast<LPARAM>(tmpl.pattern));
    }

    // 选择第一项
    SendMessage(m_hRegexTemplateCombo, CB_SETCURSEL, 0, 0);
}

void UIManager::OnCommand(WPARAM wParam, LPARAM lParam)
{
    int wmId = LOWORD(wParam);
    int wmEvent = HIWORD(wParam);

    switch (wmId) {
    case ID_COMBO_REGEX_TEMPLATES:
        if (wmEvent == CBN_SELCHANGE) {
            OnRegexTemplateSelected();
        }
        break;

    case ID_BUTTON_REGEX_HELP:
        OnRegexHelp();
        break;

    default:
        // 其他命令处理
        break;
    }
}

void UIManager::OnNotify(WPARAM wParam, LPARAM lParam)
{
    LPNMHDR pnmh = reinterpret_cast<LPNMHDR>(lParam);

    switch (pnmh->code) {
    case LVN_ITEMCHANGED:
        if (pnmh->hwndFrom == m_hFileListView) {
            OnFileListSelectionChanged();
        }
        break;

    case NM_DBLCLK:
        if (pnmh->hwndFrom == m_hFileListView || pnmh->hwndFrom == m_hPreviewListView) {
            // 双击列表项时的处理
            OnPreview();
        }
        break;

    default:
        break;
    }
}

void UIManager::OnRegexTemplateSelected()
{
    if (!m_hRegexTemplateCombo || !m_hFindEdit) return;

    int selectedIndex = static_cast<int>(SendMessage(m_hRegexTemplateCombo, CB_GETCURSEL, 0, 0));
    if (selectedIndex > 0) { // 跳过第一项"选择模板..."
        wchar_t pattern[256];
        SendMessage(m_hRegexTemplateCombo, CB_GETLBTEXT, selectedIndex,
                   reinterpret_cast<LPARAM>(pattern));

        // 获取存储的模式
        LPARAM patternData = SendMessage(m_hRegexTemplateCombo, CB_GETITEMDATA, selectedIndex, 0);
        if (patternData != CB_ERR) {
            const wchar_t* regexPattern = reinterpret_cast<const wchar_t*>(patternData);
            SetWindowText(m_hFindEdit, regexPattern);

            // 自动勾选正则表达式复选框
            if (m_hRegexCheck) {
                SendMessage(m_hRegexCheck, BM_SETCHECK, BST_CHECKED, 0);
            }
        }
    }
}

void UIManager::OnRegexHelp()
{
    ShowRegexHelpDialog();
}

void UIManager::ShowRegexHelpDialog()
{
    const wchar_t* helpText =
        L"常用正则表达式语法：\n\n"
        L"基本字符：\n"
        L"  .     匹配任意字符（除换行符）\n"
        L"  \\d    匹配数字 (0-9)\n"
        L"  \\w    匹配字母、数字、下划线\n"
        L"  \\s    匹配空白字符\n\n"
        L"量词：\n"
        L"  *     匹配0次或多次\n"
        L"  +     匹配1次或多次\n"
        L"  ?     匹配0次或1次\n"
        L"  {n}   匹配n次\n"
        L"  {n,m} 匹配n到m次\n\n"
        L"字符类：\n"
        L"  [abc] 匹配a、b或c\n"
        L"  [a-z] 匹配小写字母\n"
        L"  [^abc] 匹配除a、b、c之外的字符\n\n"
        L"锚点：\n"
        L"  ^     行开始\n"
        L"  $     行结束\n\n"
        L"分组：\n"
        L"  ()    分组，可在替换中使用$1、$2引用\n\n"
        L"示例：\n"
        L"  查找：(\\d+)  替换：[$1]  将数字用方括号包围\n"
        L"  查找：\\s+    替换：_     将空格替换为下划线";

    MessageBox(m_hMainWnd, helpText, L"正则表达式帮助", MB_OK | MB_ICONINFORMATION);
}

void UIManager::OnSelectFiles()
{
    // 这个方法由FileRenamerApp处理
}

void UIManager::OnSelectFolder()
{
    // 这个方法由FileRenamerApp处理
}

void UIManager::OnClearList()
{
    // 这个方法由FileRenamerApp处理
}

void UIManager::OnPreview()
{
    // 这个方法由FileRenamerApp处理
}

void UIManager::OnExecute()
{
    // 这个方法由FileRenamerApp处理
}

void UIManager::OnUndo()
{
    // 这个方法由FileRenamerApp处理
}

void UIManager::OnFileListSelectionChanged()
{
    // 当文件列表选择改变时，可以更新状态栏信息
    if (!m_hFileListView) return;

    int selectedCount = ListView_GetSelectedCount(m_hFileListView);
    int totalCount = ListView_GetItemCount(m_hFileListView);

    std::wstring statusText = L"选中 " + std::to_wstring(selectedCount) +
                             L" / " + std::to_wstring(totalCount) + L" 个文件";
    SetStatusText(statusText);
}

void UIManager::OnDropFiles(HDROP hDrop)
{
    if (!hDrop) return;

    UINT fileCount = DragQueryFile(hDrop, 0xFFFFFFFF, nullptr, 0);
    std::vector<std::wstring> droppedFiles;

    for (UINT i = 0; i < fileCount; ++i) {
        UINT pathLength = DragQueryFile(hDrop, i, nullptr, 0);
        if (pathLength > 0) {
            std::wstring filePath(pathLength, L'\0');
            DragQueryFile(hDrop, i, &filePath[0], pathLength + 1);
            filePath.resize(pathLength); // 移除多余的null字符
            droppedFiles.push_back(filePath);
        }
    }

    DragFinish(hDrop);

    // 通知应用程序处理拖拽的文件
    if (!droppedFiles.empty() && g_pApp && g_pApp->GetFileManager()) {
        g_pApp->GetFileManager()->AddFiles(droppedFiles);
        if (g_pApp->GetUIManager()) {
            g_pApp->GetUIManager()->UpdateFileList(g_pApp->GetFileManager()->GetFiles());
        }
        g_pApp->UpdateStatus(L"已添加 " + std::to_wstring(droppedFiles.size()) + L" 个文件");
    }
}
