#include "RenameEngine.h"
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <filesystem>

namespace fs = std::filesystem;

RenameEngine::RenameEngine()
{
}

RenameEngine::~RenameEngine()
{
}

void RenameEngine::SetRule(const RenameRule& rule)
{
    m_rule = rule;
}

void RenameEngine::ClearRule()
{
    m_rule = RenameRule();
}

bool RenameEngine::GeneratePreview(const std::vector<FileInfo>& files, std::vector<FileInfo>& previewFiles)
{
    previewFiles.clear();
    previewFiles.reserve(files.size());

    try {
        for (size_t i = 0; i < files.size(); ++i) {
            if (files[i].isSelected) {
                FileInfo previewFile = files[i];
                previewFile.newName = ApplyRule(files[i], static_cast<int>(i));
                previewFiles.push_back(previewFile);
            }
        }
        return true;
    }
    catch (const std::exception&) {
        return false;
    }
}

std::wstring RenameEngine::PreviewSingleFile(const FileInfo& file)
{
    try {
        return ApplyRule(file, 0);
    }
    catch (const std::exception&) {
        return file.originalName;
    }
}

RenameResult RenameEngine::ExecuteRename(std::vector<FileInfo>& files)
{
    RenameResult result;
    result.success = true;
    result.processedCount = 0;
    result.errorCount = 0;

    // 清空撤销栈
    ClearUndoStack();

    try {
        // 首先生成所有新名称并检查冲突
        std::vector<std::pair<size_t, std::wstring>> renameList;
        for (size_t i = 0; i < files.size(); ++i) {
            if (files[i].isSelected) {
                std::wstring newName = ApplyRule(files[i], static_cast<int>(renameList.size()));
                if (newName != files[i].originalName) {
                    renameList.push_back(std::make_pair(i, newName));
                }
            }
        }

        // 检查冲突
        std::vector<std::wstring> conflicts = CheckForConflicts(files);
        if (!conflicts.empty()) {
            result.success = false;
            result.errorMessage = L"存在命名冲突: " + conflicts[0];
            return result;
        }

        // 检查目标路径是否存在
        for (const auto& rename : renameList) {
            size_t index = rename.first;
            const std::wstring& newName = rename.second;
            const FileInfo& file = files[index];
            std::wstring newPath = file.directory + L"\\" + newName;

            // 检查目标文件是否已存在（且不是自己）
            if (fs::exists(newPath) && newPath != file.originalPath) {
                result.success = false;
                result.errorMessage = L"目标文件已存在: " + newName;
                return result;
            }

            // 检查新文件名是否有效
            if (!ValidateNewName(newName)) {
                result.success = false;
                result.errorMessage = L"无效的文件名: " + newName;
                return result;
            }
        }

        // 执行重命名
        std::vector<std::pair<std::wstring, std::wstring>> successfulRenames; // 成功的重命名记录

        for (const auto& rename : renameList) {
            size_t index = rename.first;
            const std::wstring& newName = rename.second;

            FileInfo& file = files[index];
            std::wstring newPath = file.directory + L"\\" + newName;

            if (RenameFile(file.originalPath, newPath)) {
                // 记录成功的重命名
                successfulRenames.push_back(std::make_pair(newPath, file.originalPath));

                // 更新文件信息
                file.originalPath = newPath;
                file.originalName = newName;
                file.newName = newName;

                result.processedCount++;
                result.renamedFiles.push_back(std::make_pair(file.originalName, newName));
            }
            else {
                result.errorCount++;

                // 如果发生错误，回滚之前的重命名
                if (!successfulRenames.empty()) {
                    for (auto it = successfulRenames.rbegin(); it != successfulRenames.rend(); ++it) {
                        RenameFile(it->first, it->second); // 尝试回滚
                    }
                    result.errorMessage = L"重命名失败，已回滚所有更改: " + file.originalName;
                }
                else {
                    result.errorMessage = L"重命名失败: " + file.originalName;
                }

                result.success = false;
                return result; // 立即返回，不继续处理
            }
        }

        // 所有重命名都成功，将记录添加到撤销栈
        for (const auto& rename : successfulRenames) {
            m_undoStack.push_back(rename);
        }
    }
    catch (const std::exception& e) {
        result.success = false;
        result.errorMessage = L"重命名过程中发生错误";
    }

    return result;
}

bool RenameEngine::RenameFile(const std::wstring& oldPath, const std::wstring& newPath)
{
    try {
        return MoveFile(oldPath.c_str(), newPath.c_str()) != 0;
    }
    catch (const std::exception&) {
        return false;
    }
}

bool RenameEngine::UndoLastRename()
{
    if (m_undoStack.empty()) {
        return false;
    }

    bool success = true;
    
    // 逆序撤销
    for (auto it = m_undoStack.rbegin(); it != m_undoStack.rend(); ++it) {
        const std::wstring& currentPath = it->first;
        const std::wstring& originalPath = it->second;
        
        if (!RenameFile(currentPath, originalPath)) {
            success = false;
            break;
        }
    }
    
    if (success) {
        m_undoStack.clear();
    }
    
    return success;
}

void RenameEngine::ClearUndoStack()
{
    m_undoStack.clear();
}

bool RenameEngine::ValidateRule() const
{
    // 检查基本规则有效性
    switch (m_rule.type) {
    case RenameRuleType::SIMPLE_REPLACE:
        return !m_rule.findText.empty();
        
    case RenameRuleType::REGEX_REPLACE:
        if (m_rule.findText.empty()) {
            return false;
        }
        try {
            std::wregex regex(m_rule.findText);
            return true;
        }
        catch (const std::exception&) {
            return false;
        }
        
    case RenameRuleType::ADD_NUMBER:
        return m_rule.addNumber && m_rule.numberStep > 0;
        
    default:
        return true;
    }
}

bool RenameEngine::ValidateNewName(const std::wstring& newName) const
{
    if (newName.empty()) {
        return false;
    }
    
    // 检查无效字符
    const std::wstring invalidChars = L"<>:\"/\\|?*";
    for (wchar_t c : invalidChars) {
        if (newName.find(c) != std::wstring::npos) {
            return false;
        }
    }
    
    // 检查保留名称
    const std::vector<std::wstring> reservedNames = {
        L"CON", L"PRN", L"AUX", L"NUL",
        L"COM1", L"COM2", L"COM3", L"COM4", L"COM5", L"COM6", L"COM7", L"COM8", L"COM9",
        L"LPT1", L"LPT2", L"LPT3", L"LPT4", L"LPT5", L"LPT6", L"LPT7", L"LPT8", L"LPT9"
    };
    
    std::wstring upperName = newName;
    std::transform(upperName.begin(), upperName.end(), upperName.begin(), ::towupper);
    
    for (const auto& reserved : reservedNames) {
        if (upperName == reserved) {
            return false;
        }
    }
    
    return true;
}

std::vector<std::wstring> RenameEngine::CheckForConflicts(const std::vector<FileInfo>& files)
{
    std::vector<std::wstring> conflicts;
    std::vector<std::wstring> newNames;
    
    // 收集所有新名称
    for (size_t i = 0; i < files.size(); ++i) {
        if (files[i].isSelected) {
            std::wstring newName = ApplyRule(files[i], static_cast<int>(newNames.size()));
            newNames.push_back(newName);
        }
    }
    
    // 检查重复
    std::sort(newNames.begin(), newNames.end());
    auto it = std::adjacent_find(newNames.begin(), newNames.end());
    while (it != newNames.end()) {
        conflicts.push_back(*it);
        it = std::adjacent_find(it + 1, newNames.end());
    }
    
    return conflicts;
}

std::wstring RenameEngine::ApplyRule(const FileInfo& file, int index)
{
    std::wstring result = file.originalName;

    // 分离文件名和扩展名
    std::wstring nameWithoutExt = result;
    std::wstring extension = L"";

    if (m_rule.preserveExtension && !file.extension.empty()) {
        size_t extPos = result.find_last_of(L'.');
        if (extPos != std::wstring::npos) {
            nameWithoutExt = result.substr(0, extPos);
            extension = result.substr(extPos);
        }
    }

    // 应用规则
    switch (m_rule.type) {
    case RenameRuleType::SIMPLE_REPLACE:
        nameWithoutExt = ApplySimpleReplace(nameWithoutExt);
        break;

    case RenameRuleType::REGEX_REPLACE:
        nameWithoutExt = ApplyRegexReplace(nameWithoutExt);
        break;

    case RenameRuleType::ADD_PREFIX:
        nameWithoutExt = m_rule.replaceText + nameWithoutExt;
        break;

    case RenameRuleType::ADD_SUFFIX:
        nameWithoutExt = nameWithoutExt + m_rule.replaceText;
        break;

    case RenameRuleType::ADD_NUMBER:
        nameWithoutExt = ApplyNumbering(nameWithoutExt, index);
        break;

    case RenameRuleType::CHANGE_CASE:
        nameWithoutExt = ApplyCaseChange(nameWithoutExt);
        break;

    case RenameRuleType::REMOVE_CHARS:
        // 移除指定字符
        for (wchar_t c : m_rule.findText) {
            nameWithoutExt.erase(std::remove(nameWithoutExt.begin(), nameWithoutExt.end(), c),
                               nameWithoutExt.end());
        }
        break;

    default:
        break;
    }

    // 组合结果
    result = nameWithoutExt + extension;

    // 清理无效字符
    result = SanitizeFileName(result);

    return result;
}

std::wstring RenameEngine::ApplySimpleReplace(const std::wstring& input)
{
    if (m_rule.findText.empty()) {
        return input;
    }

    std::wstring result = input;
    std::wstring findText = m_rule.findText;
    std::wstring replaceText = m_rule.replaceText;

    // 如果不区分大小写，转换为小写进行比较
    if (!m_rule.caseSensitive) {
        std::wstring lowerInput = input;
        std::wstring lowerFind = findText;
        std::transform(lowerInput.begin(), lowerInput.end(), lowerInput.begin(), ::towlower);
        std::transform(lowerFind.begin(), lowerFind.end(), lowerFind.begin(), ::towlower);

        size_t pos = 0;
        while ((pos = lowerInput.find(lowerFind, pos)) != std::wstring::npos) {
            result.replace(pos, findText.length(), replaceText);
            lowerInput.replace(pos, findText.length(), replaceText);
            pos += replaceText.length();
        }
    }
    else {
        size_t pos = 0;
        while ((pos = result.find(findText, pos)) != std::wstring::npos) {
            result.replace(pos, findText.length(), replaceText);
            pos += replaceText.length();
        }
    }

    return result;
}

std::wstring RenameEngine::ApplyRegexReplace(const std::wstring& input)
{
    try {
        std::wregex regex(m_rule.findText,
                         m_rule.caseSensitive ? std::regex_constants::ECMAScript :
                         std::regex_constants::ECMAScript | std::regex_constants::icase);
        return std::regex_replace(input, regex, m_rule.replaceText);
    }
    catch (const std::exception&) {
        return input; // 如果正则表达式无效，返回原始输入
    }
}

std::wstring RenameEngine::ApplyNumbering(const std::wstring& input, int index)
{
    if (!m_rule.addNumber) {
        return input;
    }

    int number = m_rule.numberStart + (index * m_rule.numberStep);

    std::wostringstream oss;
    if (m_rule.numberPadding > 0) {
        oss << std::setfill(L'0') << std::setw(m_rule.numberPadding);
    }
    oss << number;

    std::wstring numberStr = m_rule.numberPrefix + oss.str() + m_rule.numberSuffix;

    // 根据替换文本决定数字位置
    if (m_rule.replaceText.empty()) {
        return input + numberStr; // 默认添加到末尾
    }
    else {
        // 使用替换文本作为模板，{n}表示数字位置
        std::wstring result = m_rule.replaceText;
        size_t pos = result.find(L"{n}");
        if (pos != std::wstring::npos) {
            result.replace(pos, 3, numberStr);
        }
        else {
            result += numberStr;
        }
        return result;
    }
}

std::wstring RenameEngine::ApplyCaseChange(const std::wstring& input)
{
    std::wstring result = input;

    switch (m_rule.caseType) {
    case CaseType::UPPER:
        std::transform(result.begin(), result.end(), result.begin(), ::towupper);
        break;

    case CaseType::LOWER:
        std::transform(result.begin(), result.end(), result.begin(), ::towlower);
        break;

    case CaseType::TITLE:
        if (!result.empty()) {
            result[0] = ::towupper(result[0]);
            for (size_t i = 1; i < result.length(); ++i) {
                if (result[i - 1] == L' ' || result[i - 1] == L'-' || result[i - 1] == L'_') {
                    result[i] = ::towupper(result[i]);
                }
                else {
                    result[i] = ::towlower(result[i]);
                }
            }
        }
        break;

    case CaseType::SENTENCE:
        if (!result.empty()) {
            result[0] = ::towupper(result[0]);
            for (size_t i = 1; i < result.length(); ++i) {
                result[i] = ::towlower(result[i]);
            }
        }
        break;
    }

    return result;
}

bool RenameEngine::IsValidFileName(const std::wstring& fileName) const
{
    return ValidateNewName(fileName);
}

std::wstring RenameEngine::SanitizeFileName(const std::wstring& fileName)
{
    std::wstring result = fileName;

    // 移除或替换无效字符
    const std::wstring invalidChars = L"<>:\"/\\|?*";
    for (wchar_t& c : result) {
        if (invalidChars.find(c) != std::wstring::npos) {
            c = L'_'; // 替换为下划线
        }
    }

    // 移除开头和结尾的空格和点
    while (!result.empty() && (result.front() == L' ' || result.front() == L'.')) {
        result.erase(0, 1);
    }
    while (!result.empty() && (result.back() == L' ' || result.back() == L'.')) {
        result.pop_back();
    }

    // 确保不为空
    if (result.empty()) {
        result = L"renamed_file";
    }

    return result;
}
