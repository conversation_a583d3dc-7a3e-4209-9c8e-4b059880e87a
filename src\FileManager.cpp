#include "FileManager.h"
#include <algorithm>
#include <filesystem>

namespace fs = std::filesystem;

FileManager::FileManager()
{
}

FileManager::~FileManager()
{
}

bool FileManager::AddFiles(const std::vector<std::wstring>& filePaths)
{
    bool success = true;
    
    for (const auto& path : filePaths) {
        FileInfo info;
        if (GetFileInfo(path, info)) {
            // 检查是否已存在
            auto it = std::find_if(m_files.begin(), m_files.end(),
                [&path](const FileInfo& file) {
                    return file.originalPath == path;
                });
            
            if (it == m_files.end()) {
                m_files.push_back(info);
            }
        }
        else {
            success = false;
        }
    }
    
    return success;
}

bool FileManager::AddFolder(const std::wstring& folderPath, bool recursive)
{
    try {
        if (!fs::exists(folderPath) || !fs::is_directory(folderPath)) {
            return false;
        }
        
        EnumerateFolder(folderPath, recursive);
        return true;
    }
    catch (const std::exception&) {
        return false;
    }
}

void FileManager::ClearFiles()
{
    m_files.clear();
}

void FileManager::RemoveFile(size_t index)
{
    if (index < m_files.size()) {
        m_files.erase(m_files.begin() + index);
    }
}

void FileManager::RemoveSelectedFiles()
{
    m_files.erase(
        std::remove_if(m_files.begin(), m_files.end(),
            [](const FileInfo& file) { return file.isSelected; }),
        m_files.end());
}

FileInfo* FileManager::GetFile(size_t index)
{
    if (index < m_files.size()) {
        return &m_files[index];
    }
    return nullptr;
}

const FileInfo* FileManager::GetFile(size_t index) const
{
    if (index < m_files.size()) {
        return &m_files[index];
    }
    return nullptr;
}

void FileManager::SelectAll()
{
    for (auto& file : m_files) {
        file.isSelected = true;
    }
}

void FileManager::UnselectAll()
{
    for (auto& file : m_files) {
        file.isSelected = false;
    }
}

void FileManager::SetFileSelected(size_t index, bool selected)
{
    if (index < m_files.size()) {
        m_files[index].isSelected = selected;
    }
}

size_t FileManager::GetSelectedCount() const
{
    return std::count_if(m_files.begin(), m_files.end(),
        [](const FileInfo& file) { return file.isSelected; });
}

bool FileManager::ValidateFiles() const
{
    for (const auto& file : m_files) {
        if (!CheckFileExists(file.originalPath)) {
            return false;
        }
    }
    return true;
}

bool FileManager::CheckForDuplicateNames() const
{
    std::vector<std::wstring> names;
    for (const auto& file : m_files) {
        if (file.isSelected) {
            names.push_back(file.newName);
        }
    }
    
    std::sort(names.begin(), names.end());
    return std::adjacent_find(names.begin(), names.end()) != names.end();
}

bool FileManager::CheckFileExists(const std::wstring& path) const
{
    try {
        return fs::exists(path);
    }
    catch (const std::exception&) {
        return false;
    }
}

void FileManager::SortByName(bool ascending)
{
    std::sort(m_files.begin(), m_files.end(),
        [ascending](const FileInfo& a, const FileInfo& b) {
            if (ascending) {
                return a.originalName < b.originalName;
            }
            else {
                return a.originalName > b.originalName;
            }
        });
}

void FileManager::SortByPath(bool ascending)
{
    std::sort(m_files.begin(), m_files.end(),
        [ascending](const FileInfo& a, const FileInfo& b) {
            if (ascending) {
                return a.originalPath < b.originalPath;
            }
            else {
                return a.originalPath > b.originalPath;
            }
        });
}

void FileManager::SortBySize(bool ascending)
{
    std::sort(m_files.begin(), m_files.end(),
        [ascending](const FileInfo& a, const FileInfo& b) {
            if (ascending) {
                return a.fileSize.QuadPart < b.fileSize.QuadPart;
            }
            else {
                return a.fileSize.QuadPart > b.fileSize.QuadPart;
            }
        });
}

void FileManager::SortByDate(bool ascending)
{
    std::sort(m_files.begin(), m_files.end(),
        [ascending](const FileInfo& a, const FileInfo& b) {
            ULARGE_INTEGER timeA, timeB;
            timeA.LowPart = a.lastWriteTime.dwLowDateTime;
            timeA.HighPart = a.lastWriteTime.dwHighDateTime;
            timeB.LowPart = b.lastWriteTime.dwLowDateTime;
            timeB.HighPart = b.lastWriteTime.dwHighDateTime;
            
            if (ascending) {
                return timeA.QuadPart < timeB.QuadPart;
            }
            else {
                return timeA.QuadPart > timeB.QuadPart;
            }
        });
}

bool FileManager::GetFileInfo(const std::wstring& path, FileInfo& info)
{
    WIN32_FIND_DATA findData;
    HANDLE hFind = FindFirstFile(path.c_str(), &findData);
    
    if (hFind == INVALID_HANDLE_VALUE) {
        return false;
    }
    
    FindClose(hFind);
    
    info.originalPath = path;
    info.originalName = GetFileName(path);
    info.directory = GetDirectory(path);
    info.extension = GetFileExtension(info.originalName);
    info.isDirectory = (findData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY) != 0;
    info.attributes = findData.dwFileAttributes;
    info.lastWriteTime = findData.ftLastWriteTime;
    info.fileSize.LowPart = findData.nFileSizeLow;
    info.fileSize.HighPart = findData.nFileSizeHigh;
    info.isSelected = true;
    info.newName = info.originalName; // 初始时新名称等于原名称
    
    return true;
}

void FileManager::EnumerateFolder(const std::wstring& folderPath, bool recursive)
{
    try {
        if (recursive) {
            for (const auto& entry : fs::recursive_directory_iterator(folderPath)) {
                if (entry.is_regular_file()) {
                    FileInfo info;
                    if (GetFileInfo(entry.path().wstring(), info)) {
                        m_files.push_back(info);
                    }
                }
            }
        }
        else {
            for (const auto& entry : fs::directory_iterator(folderPath)) {
                if (entry.is_regular_file()) {
                    FileInfo info;
                    if (GetFileInfo(entry.path().wstring(), info)) {
                        m_files.push_back(info);
                    }
                }
            }
        }
    }
    catch (const std::exception&) {
        // 忽略访问错误
    }
}

std::wstring FileManager::GetFileExtension(const std::wstring& fileName)
{
    size_t pos = fileName.find_last_of(L'.');
    if (pos != std::wstring::npos && pos < fileName.length() - 1) {
        return fileName.substr(pos);
    }
    return L"";
}

std::wstring FileManager::GetFileName(const std::wstring& path)
{
    size_t pos = path.find_last_of(L"\\/");
    if (pos != std::wstring::npos) {
        return path.substr(pos + 1);
    }
    return path;
}

std::wstring FileManager::GetDirectory(const std::wstring& path)
{
    size_t pos = path.find_last_of(L"\\/");
    if (pos != std::wstring::npos) {
        return path.substr(0, pos);
    }
    return L"";
}
