#pragma once

#include "main.h"
#include "FileManager.h"
#include <regex>

// 重命名规则类型
enum class RenameRuleType {
    SIMPLE_REPLACE,     // 简单文本替换
    REGEX_REPLACE,      // 正则表达式替换
    ADD_PREFIX,         // 添加前缀
    ADD_SUFFIX,         // 添加后缀
    ADD_NUMBER,         // 添加序号
    CHANGE_CASE,        // 改变大小写
    REMOVE_CHARS,       // 移除字符
    CUSTOM              // 自定义规则
};

// 大小写转换类型
enum class CaseType {
    UPPER,              // 全部大写
    LOWER,              // 全部小写
    TITLE,              // 首字母大写
    SENTENCE            // 句子格式
};

// 重命名规则结构
struct RenameRule {
    RenameRuleType type;
    std::wstring findText;          // 查找文本
    std::wstring replaceText;       // 替换文本
    bool useRegex;                  // 是否使用正则表达式
    bool caseSensitive;             // 是否区分大小写
    bool addNumber;                 // 是否添加序号
    int numberStart;                // 序号起始值
    int numberStep;                 // 序号步长
    int numberPadding;              // 序号填充位数
    std::wstring numberPrefix;      // 序号前缀
    std::wstring numberSuffix;      // 序号后缀
    CaseType caseType;              // 大小写类型
    bool preserveExtension;         // 是否保留扩展名

    RenameRule() : type(RenameRuleType::SIMPLE_REPLACE), useRegex(false), 
                   caseSensitive(false), addNumber(false), numberStart(1), 
                   numberStep(1), numberPadding(0), caseType(CaseType::LOWER),
                   preserveExtension(true) {}
};

// 重命名结果
struct RenameResult {
    bool success;
    std::wstring errorMessage;
    size_t processedCount;
    size_t errorCount;
    std::vector<std::pair<std::wstring, std::wstring>> renamedFiles; // 原名->新名

    RenameResult() : success(false), processedCount(0), errorCount(0) {}
};

// 重命名引擎类
class RenameEngine {
public:
    RenameEngine();
    ~RenameEngine();

    // 规则管理
    void SetRule(const RenameRule& rule);
    const RenameRule& GetRule() const { return m_rule; }
    void ClearRule();

    // 预览功能
    bool GeneratePreview(const std::vector<FileInfo>& files, std::vector<FileInfo>& previewFiles);
    std::wstring PreviewSingleFile(const FileInfo& file);

    // 执行重命名
    RenameResult ExecuteRename(std::vector<FileInfo>& files);
    bool RenameFile(const std::wstring& oldPath, const std::wstring& newPath);

    // 撤销功能
    bool CanUndo() const { return !m_undoStack.empty(); }
    bool UndoLastRename();
    void ClearUndoStack();

    // 验证功能
    bool ValidateRule() const;
    bool ValidateNewName(const std::wstring& newName) const;
    std::vector<std::wstring> CheckForConflicts(const std::vector<FileInfo>& files);

private:
    RenameRule m_rule;
    std::vector<std::pair<std::wstring, std::wstring>> m_undoStack; // 新名->原名
    
    // 内部处理方法
    std::wstring ApplyRule(const FileInfo& file, int index = 0);
    std::wstring ApplySimpleReplace(const std::wstring& input);
    std::wstring ApplyRegexReplace(const std::wstring& input);
    std::wstring ApplyNumbering(const std::wstring& input, int index);
    std::wstring ApplyCaseChange(const std::wstring& input);
    std::wstring RemoveInvalidChars(const std::wstring& input);
    
    // 辅助方法
    bool IsValidFileName(const std::wstring& fileName) const;
    std::wstring SanitizeFileName(const std::wstring& fileName);
};
