#pragma once

#include "main.h"
#include <filesystem>

// 文件信息结构
struct FileInfo {
    std::wstring originalPath;      // 原始完整路径
    std::wstring originalName;      // 原始文件名
    std::wstring newName;           // 新文件名
    std::wstring directory;         // 所在目录
    std::wstring extension;         // 文件扩展名
    bool isDirectory;               // 是否为目录
    bool isSelected;                // 是否被选中
    DWORD attributes;               // 文件属性
    FILETIME lastWriteTime;         // 最后修改时间
    LARGE_INTEGER fileSize;         // 文件大小

    FileInfo() : isDirectory(false), isSelected(true), attributes(0) {
        fileSize.QuadPart = 0;
        ZeroMemory(&lastWriteTime, sizeof(FILETIME));
    }
};

// 文件管理器类
class FileManager {
public:
    FileManager();
    ~FileManager();

    // 文件操作
    bool AddFiles(const std::vector<std::wstring>& filePaths);
    bool AddFolder(const std::wstring& folderPath, bool recursive = false);
    void ClearFiles();
    void RemoveFile(size_t index);
    void RemoveSelectedFiles();

    // 获取文件信息
    const std::vector<FileInfo>& GetFiles() const { return m_files; }
    size_t GetFileCount() const { return m_files.size(); }
    FileInfo* GetFile(size_t index);
    const FileInfo* GetFile(size_t index) const;

    // 选择操作
    void SelectAll();
    void UnselectAll();
    void SetFileSelected(size_t index, bool selected);
    size_t GetSelectedCount() const;

    // 文件验证
    bool ValidateFiles() const;
    bool CheckForDuplicateNames() const;
    bool CheckFileExists(const std::wstring& path) const;

    // 排序
    void SortByName(bool ascending = true);
    void SortByPath(bool ascending = true);
    void SortBySize(bool ascending = true);
    void SortByDate(bool ascending = true);

private:
    std::vector<FileInfo> m_files;
    
    // 辅助方法
    bool GetFileInfo(const std::wstring& path, FileInfo& info);
    void EnumerateFolder(const std::wstring& folderPath, bool recursive);
    std::wstring GetFileExtension(const std::wstring& fileName);
    std::wstring GetFileName(const std::wstring& path);
    std::wstring GetDirectory(const std::wstring& path);
};
