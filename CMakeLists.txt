cmake_minimum_required(VERSION 3.16)

project(FileRenamer 
    VERSION 1.0.0
    DESCRIPTION "文件批量重命名工具"
    LANGUAGES CXX RC)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# 编译选项
if(MSVC)
    add_compile_options(/W4 /WX- /utf-8)
    add_compile_definitions(_UNICODE UNICODE WIN32_LEAN_AND_MEAN)
    # 设置子系统为Windows
    set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} /SUBSYSTEM:WINDOWS")
endif()

# 源文件
set(SOURCES
    src/main.cpp
    src/FileRenamerApp.cpp
    src/FileManager.cpp
    src/RenameEngine.cpp
    src/UIManager.cpp
)

# 头文件
set(HEADERS
    src/main.h
    src/FileRenamerApp.h
    src/FileManager.h
    src/RenameEngine.h
    src/UIManager.h
    src/resource.h
)

# 资源文件
set(RESOURCES
    src/FileRenamer.rc
)

# 创建可执行文件
add_executable(${PROJECT_NAME} WIN32
    ${SOURCES}
    ${HEADERS}
    ${RESOURCES}
)

# 链接库
target_link_libraries(${PROJECT_NAME}
    comctl32
    comdlg32
    shell32
    ole32
    oleaut32
    uuid
    shlwapi
)

# 包含目录
target_include_directories(${PROJECT_NAME} PRIVATE
    src
)

# 预处理器定义
target_compile_definitions(${PROJECT_NAME} PRIVATE
    _UNICODE
    UNICODE
    WIN32_LEAN_AND_MEAN
    NOMINMAX
)

# 设置启动项目
set_property(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR} PROPERTY VS_STARTUP_PROJECT ${PROJECT_NAME})

# 安装规则
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
)

# 打包配置
set(CPACK_PACKAGE_NAME "FileRenamer")
set(CPACK_PACKAGE_VENDOR "File Renamer")
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "文件批量重命名工具")
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_INSTALL_DIRECTORY "FileRenamer")
set(CPACK_GENERATOR "NSIS")

include(CPack)
